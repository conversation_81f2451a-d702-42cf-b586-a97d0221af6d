import axios, { AxiosInstance, AxiosResponse } from 'axios';

// API Configuration
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000';
const API_VERSION = '/api/v1';

// Create axios instance
const apiClient: AxiosInstance = axios.create({
  baseURL: `${API_BASE_URL}${API_VERSION}`,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Token management
let authToken: string | null = localStorage.getItem('admin_token');

// Request interceptor
apiClient.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);

    // For admin routes, add admin access header
    if (config.url?.includes('/admin/')) {
      config.headers['x-admin-access'] = 'true';
      config.headers['x-admin-token'] = authToken || 'admin-access-token';
    }

    // Add auth token for other routes
    if (authToken && !config.url?.includes('/admin/')) {
      config.headers.Authorization = `Bearer ${authToken}`;
    }

    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    console.log(`API Response: ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error('API Response Error:', error.response?.data || error.message);

    if (error.response?.status === 401) {
      // Handle unauthorized access
      console.error('Unauthorized access - clearing token');
      clearAuthToken();
      // Redirect to login if needed
      if (window.location.pathname !== '/login') {
        window.location.href = '/login';
      }
    }

    return Promise.reject(error);
  }
);

// Token management functions
export const setAuthToken = (token: string) => {
  authToken = token;
  localStorage.setItem('admin_token', token);
};

export const clearAuthToken = () => {
  authToken = null;
  localStorage.removeItem('admin_token');
};

export const getAuthToken = () => {
  return authToken;
};

// Types
export interface User {
  id: string;
  userId: string;
  fullName: string;
  email: string;
  role: string;
  facility: string;
  state: string;
  status: 'active' | 'inactive' | 'suspended';
  lastLogin: string;
  deviceId: string;
  registeredAt: string;
  isActive: boolean;
  deviceInfo: {
    deviceId: string;
    activationKeyId: string;
    isActivated: boolean;
    activatedAt?: string;
    deviceModel?: string;
    osVersion?: string;
    appVersion?: string;
  };
  activationKeyInfo?: {
    keyId: string;
    shortCode: string;
    status: string;
    expiresAt: string;
    remainingDays: number;
    createdBy: {
      adminId: string;
      adminName: string;
      adminEmail: string;
    };
  };
}

export interface ActivationKey {
  keyId: string;
  activationKey: string;
  shortCode: string;
  userId: string;
  deviceId: string;
  role: string;
  facility: string;
  state: string;
  validityMonths: number;
  issuedAt: string;
  expiresAt: string;
  status: 'active' | 'used' | 'expired' | 'revoked';
  isUsed: boolean;
  usedAt?: string;
  createdBy: {
    adminId: string;
    adminName: string;
    adminEmail: string;
  };
  notes?: string;
  adminNotes?: string;
}

export interface CreateUserRequest {
  fullName: string;
  email: string;
  role: string;
  facility: string;
  state: string;
  contactInfo: string;
  deviceId: string;
  validityMonths: number;
  notes?: string;
}

export interface DashboardStats {
  users: {
    total: number;
    active: number;
    inactive: number;
  };
  activationKeys: {
    total: number;
    active: number;
    used: number;
    expired: number;
    revoked: number;
  };
  recent: {
    users: {
      userId: string;
      fullName: string;
      role: string;
      facility: string;
      createdAt: string;
    }[];
    keys: {
      keyId: string;
      shortCode: string;
      status: string;
      facility: string;
      createdAt: string;
      createdBy: {
        adminName: string;
      };
    }[];
  };
}

// API Service Class
class ApiService {
  // Health check
  async healthCheck() {
    const response = await apiClient.get('/health');
    return response.data;
  }

  // Authentication API
  async adminLogin(credentials: { email: string; password: string }) {
    // Simple admin login - in production, this would validate against admin credentials
    // For now, we'll mock a successful login for admin users
    if (credentials.email.includes('admin') || credentials.email.includes('nso')) {
      const mockToken = 'mock-admin-token-' + Date.now();
      setAuthToken(mockToken);

      return {
        success: true,
        data: {
          user: {
            id: 'admin-1',
            email: credentials.email,
            firstName: 'Admin',
            lastName: 'User',
            role: 'admin',
            facility: 'NSO Headquarters',
            isActive: true
          },
          token: mockToken,
          expiresIn: '24h'
        }
      };
    } else {
      return {
        success: false,
        error: 'Invalid admin credentials',
        code: 'INVALID_CREDENTIALS'
      };
    }
  }

  async logout() {
    try {
      await apiClient.post('/auth/logout');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      clearAuthToken();
    }
  }

  async verifyToken() {
    const response = await apiClient.get('/auth/verify');
    return response.data;
  }

  // Users API
  async getUsers(params: {
    page?: number;
    limit?: number;
    search?: string;
    status?: string;
    role?: string;
    facility?: string;
    state?: string;
  } = {}) {
    const response = await apiClient.get('/admin/users', { params });
    return response.data;
  }

  async getUser(userId: string) {
    const response = await apiClient.get(`/admin/users/${userId}`);
    return response.data;
  }

  async createUser(userData: CreateUserRequest) {
    try {
      console.log('Creating user with data:', userData);
      const response = await apiClient.post('/admin/users', userData);
      console.log('User creation response:', response.data);
      return response.data;
    } catch (error) {
      console.error('User creation error:', error);
      throw error;
    }
  }

  async updateUser(userId: string, userData: Partial<User>) {
    const response = await apiClient.put(`/admin/users/${userId}`, userData);
    return response.data;
  }

  async updateUserStatus(userId: string, isActive: boolean, reason?: string) {
    const response = await apiClient.put(`/admin/users/${userId}/status`, { isActive, reason });
    return response.data;
  }

  async getUserDetails(userId: string) {
    const response = await apiClient.get(`/admin/users/${userId}`);
    return response.data;
  }

  // Activation Keys API
  async getActivationKeys(params: {
    page?: number;
    limit?: number;
    status?: string;
    search?: string;
  } = {}) {
    const response = await apiClient.get('/admin/activation-keys', { params });
    return response.data;
  }

  async createActivationKey(keyData: {
    assignedTo: {
      email: string;
      fullName: string;
      role: string;
      facility?: string;
      state?: string;
    };
    validUntil: string;
    maxUses?: number;
    notes?: string;
  }) {
    try {
      console.log('Creating activation key with data:', keyData);
      const response = await apiClient.post('/admin/activation-keys', keyData);
      console.log('Activation key creation response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Activation key creation error:', error);
      throw error;
    }
  }

  async revokeActivationKey(keyId: string, reason?: string) {
    const response = await apiClient.post(`/admin/activation-keys/${keyId}/revoke`, { reason });
    return response.data;
  }

  // Dashboard API
  async getDashboardStats(): Promise<{ data: DashboardStats }> {
    const response = await apiClient.get('/admin/dashboard/stats');
    return response.data;
  }

  // Analytics API
  async getErrorAnalytics(params: {
    startDate?: string;
    endDate?: string;
  } = {}) {
    const response = await apiClient.get('/admin/analytics/errors', { params });
    return response.data;
  }

  async getUsageAnalytics(params: {
    startDate?: string;
    endDate?: string;
  } = {}) {
    const response = await apiClient.get('/admin/analytics/usage', { params });
    return response.data;
  }

  async getSystemHealth() {
    const response = await apiClient.get('/admin/system/health');
    return response.data;
  }

  // Activity Logs API - Note: These would need to be implemented in backend
  async getActivityLogs(params: {
    page?: number;
    limit?: number;
    userId?: string;
    activityType?: string;
    startDate?: string;
    endDate?: string;
  } = {}) {
    // For now, return mock data since this endpoint doesn't exist yet
    return {
      success: true,
      data: {
        activities: [],
        pagination: { page: 1, limit: 20, total: 0, pages: 0 }
      }
    };
  }

  // Sync Management API - Note: These would need to be implemented in backend
  async getSyncStatus() {
    // For now, return mock data since this endpoint doesn't exist yet
    return {
      success: true,
      data: {
        status: 'healthy',
        lastSync: new Date().toISOString(),
        pendingCount: 0
      }
    };
  }

  async triggerSync(syncType: string) {
    // For now, return mock data since this endpoint doesn't exist yet
    return {
      success: true,
      data: {
        syncId: 'mock-sync-' + Date.now(),
        status: 'initiated',
        syncType
      }
    };
  }
}

// Export singleton instance
export const apiService = new ApiService();
export default apiService;
