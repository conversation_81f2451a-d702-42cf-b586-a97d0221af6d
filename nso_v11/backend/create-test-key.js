#!/usr/bin/env node

/**
 * <PERSON>ript to create a test activation key for mobile app testing
 */

const mongoose = require('mongoose');
const ActivationKey = require('./models/ActivationKey');
const config = require('./config');

async function createTestActivationKey() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(config.MONGODB_URL, {
      serverSelectionTimeoutMS: 10000,
      connectTimeoutMS: 10000,
    });
    console.log('✅ Connected to MongoDB');

    // Generate a test activation key
    const testKey = ActivationKey.generateKey();
    console.log(`Generated test key: ${testKey}`);

    // Create the activation key document
    const crypto = require('crypto');
    const keyHash = crypto.createHash('sha256').update(testKey).digest('hex');

    const activationKey = new ActivationKey({
      key: testKey,
      keyHash: keyHash,
      assignedTo: {
        email: '<EMAIL>',
        fullName: 'Test User',
        role: 'doctor',
        facility: 'Test Hospital',
        state: 'Test State'
      },
      status: 'active',
      validFrom: new Date(),
      validUntil: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
      maxUses: 5,
      usageCount: 0,
      createdBy: new mongoose.Types.ObjectId(), // Dummy admin ID
      metadata: {
        purpose: 'Mobile app testing',
        environment: 'development'
      },
      notes: 'Test activation key for mobile app integration testing'
    });

    // Save the activation key
    await activationKey.save();
    console.log('✅ Test activation key created successfully');
    console.log(`Key: ${testKey}`);
    console.log(`Status: ${activationKey.status}`);
    console.log(`Valid until: ${activationKey.validUntil}`);
    console.log(`Max uses: ${activationKey.maxUses}`);

    // Test finding the key
    const foundKey = await ActivationKey.findByKey(testKey);
    if (foundKey) {
      console.log('✅ Key can be found in database');
      console.log(`Found key ID: ${foundKey._id}`);
    } else {
      console.log('❌ Key not found in database');
    }

    // Create a few more test keys with different roles
    const roles = ['nurse', 'technician', 'inspector'];
    for (const role of roles) {
      const key = ActivationKey.generateKey();
      const keyHash = crypto.createHash('sha256').update(key).digest('hex');

      const activationKey = new ActivationKey({
        key: key,
        keyHash: keyHash,
        assignedTo: {
          email: `${role}@example.com`,
          fullName: `Test ${role.charAt(0).toUpperCase() + role.slice(1)}`,
          role: role,
          facility: 'Test Hospital',
          state: 'Test State'
        },
        status: 'active',
        validFrom: new Date(),
        validUntil: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
        maxUses: 3,
        usageCount: 0,
        createdBy: new mongoose.Types.ObjectId(),
        metadata: {
          purpose: 'Mobile app testing',
          environment: 'development',
          role: role
        },
        notes: `Test activation key for ${role} role testing`
      });

      await activationKey.save();
      console.log(`✅ Created ${role} test key: ${key}`);
    }

    console.log('\n🎉 All test activation keys created successfully!');
    console.log('\nYou can now use these keys to test the mobile app:');
    console.log(`Main test key: ${testKey}`);

  } catch (error) {
    console.error('❌ Error creating test activation key:', error);
  } finally {
    await mongoose.connection.close();
    console.log('Database connection closed');
  }
}

// Run the script
createTestActivationKey();
