const express = require('express');
const crypto = require('crypto');
const User = require('../models/User');
const ActivationKey = require('../models/ActivationKey');
const Activity = require('../models/Activity');
const Diagnosis = require('../models/Diagnosis');
const SyncLog = require('../models/SyncLog');
const { 
  authenticateToken, 
  requireAdmin,
  requireSupervisor,
  logRequest
} = require('../middleware/auth');
const { 
  validateActivationKeyCreation,
  validatePagination,
  validateDateRange,
  validateObjectId
} = require('../middleware/validation');

const router = express.Router();

// Apply middleware to all admin routes
router.use(logRequest);

// Simple admin authentication middleware - just check for admin in the request
const simpleAdminAuth = (req, res, next) => {
  // For demo purposes, allow admin access without complex authentication
  // In production, you'd have proper admin authentication
  const mongoose = require('mongoose');
  req.user = {
    _id: new mongoose.Types.ObjectId('507f1f77bcf86cd799439011'), // Valid ObjectId for admin
    role: 'admin',
    email: '<EMAIL>',
    firstName: 'Admin',
    lastName: 'User'
  };
  next();
};

// Apply simple admin auth to all admin routes
router.use(simpleAdminAuth);

/**
 * GET /api/v1/admin/dashboard/stats
 * Get dashboard statistics for admin panel
 */
router.get('/dashboard/stats', validateDateRange, async (req, res) => {
  try {
    const {
      startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
      endDate = new Date()
    } = req.query;

    const start = new Date(startDate);
    const end = new Date(endDate);

    // Get user statistics
    const totalUsers = await User.countDocuments({ isActive: true });
    const newUsers = await User.countDocuments({
      createdAt: { $gte: start, $lte: end }
    });
    const activeUsers = await Activity.distinct('userId', {
      timestamp: { $gte: start, $lte: end }
    });

    // Get activation key statistics
    const totalKeys = await ActivationKey.countDocuments();
    const activeKeys = await ActivationKey.countDocuments({ status: 'active' });
    const usedKeys = await ActivationKey.countDocuments({ status: 'used' });
    const expiredKeys = await ActivationKey.countDocuments({ status: 'expired' });

    // Get activity statistics
    const totalActivities = await Activity.countDocuments({
      timestamp: { $gte: start, $lte: end }
    });
    const errorActivities = await Activity.countDocuments({
      activityType: 'error',
      timestamp: { $gte: start, $lte: end }
    });

    // Get diagnosis statistics
    const totalDiagnoses = await Diagnosis.countDocuments({
      createdAt: { $gte: start, $lte: end }
    });
    const completedDiagnoses = await Diagnosis.countDocuments({
      status: 'completed',
      createdAt: { $gte: start, $lte: end }
    });

    // Get sync statistics
    const totalSyncs = await SyncLog.countDocuments({
      startedAt: { $gte: start, $lte: end }
    });
    const failedSyncs = await SyncLog.countDocuments({
      status: 'failed',
      startedAt: { $gte: start, $lte: end }
    });

    // Get user role breakdown
    const userRoles = await User.aggregate([
      { $match: { isActive: true } },
      { $group: { _id: '$role', count: { $sum: 1 } } }
    ]);

    // Get top error codes
    const topErrors = await Activity.aggregate([
      {
        $match: {
          activityType: 'error',
          timestamp: { $gte: start, $lte: end }
        }
      },
      {
        $group: {
          _id: '$error.code',
          count: { $sum: 1 },
          severity: { $first: '$error.severity' }
        }
      },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]);

    res.json({
      success: true,
      data: {
        period: { startDate: start, endDate: end },
        users: {
          total: totalUsers,
          new: newUsers,
          active: activeUsers.length,
          roleBreakdown: userRoles.reduce((acc, role) => {
            acc[role._id] = role.count;
            return acc;
          }, {})
        },
        activationKeys: {
          total: totalKeys,
          active: activeKeys,
          used: usedKeys,
          expired: expiredKeys
        },
        activities: {
          total: totalActivities,
          errors: errorActivities,
          errorRate: totalActivities > 0 ? (errorActivities / totalActivities * 100).toFixed(2) : 0
        },
        diagnoses: {
          total: totalDiagnoses,
          completed: completedDiagnoses,
          completionRate: totalDiagnoses > 0 ? (completedDiagnoses / totalDiagnoses * 100).toFixed(2) : 0
        },
        sync: {
          total: totalSyncs,
          failed: failedSyncs,
          successRate: totalSyncs > 0 ? ((totalSyncs - failedSyncs) / totalSyncs * 100).toFixed(2) : 0
        },
        topErrors
      }
    });

  } catch (error) {
    console.error('Get admin dashboard stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve dashboard statistics',
      code: 'GET_ADMIN_DASHBOARD_STATS_ERROR'
    });
  }
});

/**
 * GET /api/v1/admin/users
 * Get all users with pagination and filtering
 */
router.get('/users', validatePagination, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      search,
      role,
      status,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // Build query
    const query = {};
    
    if (search) {
      query.$or = [
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { username: { $regex: search, $options: 'i' } },
        { facility: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (role) query.role = role;
    if (status === 'active') query.isActive = true;
    if (status === 'inactive') query.isActive = false;

    // Build sort object
    const sort = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    // Execute query with pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const users = await User.find(query)
      .select('-password -activationKey')
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit))
      .lean();

    // Get total count for pagination
    const total = await User.countDocuments(query);

    res.json({
      success: true,
      data: {
        users,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      }
    });

  } catch (error) {
    console.error('Get admin users error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve users',
      code: 'GET_ADMIN_USERS_ERROR'
    });
  }
});

/**
 * GET /api/v1/admin/users/:userId
 * Get detailed user information
 */
router.get('/users/:userId', validateObjectId('userId'), async (req, res) => {
  try {
    const { userId } = req.params;

    const user = await User.findById(userId)
      .select('-password')
      .lean();

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found',
        code: 'USER_NOT_FOUND'
      });
    }

    // Get user's recent activities
    const recentActivities = await Activity.find({ userId })
      .sort({ timestamp: -1 })
      .limit(10)
      .select('activityType timestamp screenName error')
      .lean();

    // Get user's diagnosis count
    const diagnosisCount = await Diagnosis.countDocuments({ userId });

    // Get user's sync statistics
    const syncStats = await SyncLog.aggregate([
      { $match: { userId: user._id } },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    res.json({
      success: true,
      data: {
        user,
        statistics: {
          diagnosisCount,
          recentActivities,
          syncStats: syncStats.reduce((acc, stat) => {
            acc[stat._id] = stat.count;
            return acc;
          }, {})
        }
      }
    });

  } catch (error) {
    console.error('Get admin user details error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve user details',
      code: 'GET_ADMIN_USER_DETAILS_ERROR'
    });
  }
});

/**
 * POST /api/v1/admin/users
 * Create new user and activation key
 */
router.post('/users', async (req, res) => {
  try {
    const {
      fullName,
      email,
      role,
      facility,
      state,
      contactInfo,
      deviceId,
      validityMonths = 12,
      notes
    } = req.body;

    // Validate required fields
    if (!fullName || !email || !role || !facility || !state || !deviceId) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: fullName, email, role, facility, state, deviceId',
        code: 'MISSING_REQUIRED_FIELDS'
      });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid email format',
        code: 'INVALID_EMAIL'
      });
    }

    // Validate role
    const validRoles = ['doctor', 'nurse', 'admin', 'technician', 'inspector', 'supervisor'];
    if (!validRoles.includes(role)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid role. Must be one of: ' + validRoles.join(', '),
        code: 'INVALID_ROLE'
      });
    }

    // Check if user with email already exists
    const existingUser = await User.findOne({ email: email.toLowerCase() });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        error: 'User with this email already exists',
        code: 'EMAIL_EXISTS'
      });
    }

    // Check if device ID is already in use
    const existingDevice = await User.findOne({ deviceId });
    if (existingDevice) {
      return res.status(400).json({
        success: false,
        error: 'Device ID is already in use',
        code: 'DEVICE_EXISTS'
      });
    }

    // Generate unique activation key
    let key;
    let isUnique = false;
    let attempts = 0;

    while (!isUnique && attempts < 10) {
      key = ActivationKey.generateKey();
      const existing = await ActivationKey.findByKey(key);
      if (!existing) {
        isUnique = true;
      }
      attempts++;
    }

    if (!isUnique) {
      return res.status(500).json({
        success: false,
        error: 'Failed to generate unique activation key',
        code: 'KEY_GENERATION_ERROR'
      });
    }

    // Calculate expiration date
    const validUntil = new Date();
    validUntil.setMonth(validUntil.getMonth() + validityMonths);

    // Create activation key first
    const activationKey = new ActivationKey({
      key,
      keyHash: crypto.createHash('sha256').update(key).digest('hex'), // Explicitly set keyHash
      assignedTo: {
        email: email.toLowerCase(),
        fullName,
        role,
        facility,
        state
      },
      validUntil,
      maxUses: 1,
      notes,
      createdBy: req.user._id,
      status: 'active'
    });

    await activationKey.save();

    // Generate license number for doctors and nurses
    const licenseNumber = (role === 'doctor' || role === 'nurse')
      ? `${role.toUpperCase()}-${state.toUpperCase()}-${Date.now().toString().slice(-6)}`
      : undefined;

    // Create user account
    const user = new User({
      username: `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      email: email.toLowerCase(),
      password: crypto.randomBytes(32).toString('hex'), // Random password
      firstName: fullName.split(' ')[0],
      lastName: fullName.split(' ').slice(1).join(' ') || fullName.split(' ')[0],
      role,
      facility,
      state,
      contactInfo,
      deviceId,
      activationKey: key,
      licenseNumber,
      isActive: true,
      isVerified: false // User needs to activate first
    });

    await user.save();

    // Add creation to activation key usage history
    await activationKey.addUsageHistory(
      'created',
      req.user._id,
      null,
      req.ip,
      null,
      { notes, createdForUser: user._id }
    );

    // Generate short code for easier sharing
    const shortCode = key.replace(/-/g, '').substring(0, 8);

    res.status(201).json({
      success: true,
      message: 'User and activation key created successfully',
      data: {
        user: {
          id: user._id,
          username: user.username,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role,
          facility: user.facility,
          state: user.state,
          contactInfo: user.contactInfo,
          deviceId: user.deviceId,
          isActive: user.isActive,
          isVerified: user.isVerified,
          createdAt: user.createdAt
        },
        activationKey: {
          keyId: activationKey._id,
          activationKey: activationKey.key,
          shortCode,
          status: activationKey.status,
          validUntil: activationKey.validUntil,
          expiresAt: activationKey.validUntil,
          createdAt: activationKey.createdAt,
          createdBy: {
            adminId: req.user._id,
            adminName: `${req.user.firstName} ${req.user.lastName}`,
            adminEmail: req.user.email
          }
        }
      }
    });

  } catch (error) {
    console.error('Create user error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create user',
      code: 'CREATE_USER_ERROR'
    });
  }
});

/**
 * PUT /api/v1/admin/users/:userId/status
 * Update user status (activate/deactivate)
 */
router.put('/users/:userId/status', validateObjectId('userId'), async (req, res) => {
  try {
    const { userId } = req.params;
    const { isActive, reason } = req.body;

    if (typeof isActive !== 'boolean') {
      return res.status(400).json({
        success: false,
        error: 'isActive must be a boolean',
        code: 'INVALID_STATUS'
      });
    }

    const user = await User.findByIdAndUpdate(
      userId,
      {
        isActive,
        $push: {
          'metadata.statusHistory': {
            status: isActive ? 'activated' : 'deactivated',
            reason: reason || 'Admin action',
            timestamp: new Date(),
            adminId: req.user._id
          }
        }
      },
      { new: true }
    ).select('-password -activationKey');

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found',
        code: 'USER_NOT_FOUND'
      });
    }

    res.json({
      success: true,
      message: `User ${isActive ? 'activated' : 'deactivated'} successfully`,
      data: { user }
    });

  } catch (error) {
    console.error('Update user status error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update user status',
      code: 'UPDATE_USER_STATUS_ERROR'
    });
  }
});

/**
 * GET /api/v1/admin/activation-keys
 * Get activation keys with pagination and filtering
 */
router.get('/activation-keys', validatePagination, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      status,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // Build query
    const query = {};
    
    if (status) query.status = status;
    if (search) {
      query.$or = [
        { 'assignedTo.email': { $regex: search, $options: 'i' } },
        { 'assignedTo.fullName': { $regex: search, $options: 'i' } },
        { 'assignedTo.facility': { $regex: search, $options: 'i' } }
      ];
    }

    // Build sort object
    const sort = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    // Execute query with pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const keys = await ActivationKey.find(query)
      .populate('createdBy', 'firstName lastName email')
      .populate('assignedTo.userId', 'firstName lastName email')
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit))
      .lean();

    // Get total count for pagination
    const total = await ActivationKey.countDocuments(query);

    res.json({
      success: true,
      data: {
        activationKeys: keys,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      }
    });

  } catch (error) {
    console.error('Get admin activation keys error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve activation keys',
      code: 'GET_ADMIN_ACTIVATION_KEYS_ERROR'
    });
  }
});

/**
 * POST /api/v1/admin/activation-keys
 * Create new activation key
 */
router.post('/activation-keys', validateActivationKeyCreation, async (req, res) => {
  try {
    const {
      assignedTo,
      validUntil,
      maxUses = 1,
      notes,
      locationRestrictions
    } = req.body;

    // Generate unique activation key
    let key;
    let isUnique = false;
    let attempts = 0;
    
    while (!isUnique && attempts < 10) {
      key = ActivationKey.generateKey();
      const existing = await ActivationKey.findByKey(key);
      if (!existing) {
        isUnique = true;
      }
      attempts++;
    }

    if (!isUnique) {
      return res.status(500).json({
        success: false,
        error: 'Failed to generate unique activation key',
        code: 'KEY_GENERATION_ERROR'
      });
    }

    const activationKey = new ActivationKey({
      key,
      keyHash: crypto.createHash('sha256').update(key).digest('hex'), // Explicitly set keyHash
      assignedTo,
      validUntil: new Date(validUntil),
      maxUses,
      notes,
      locationRestrictions,
      createdBy: req.user._id,
      status: 'active'
    });

    await activationKey.save();

    // Add creation to usage history
    await activationKey.addUsageHistory(
      'created',
      req.user._id,
      null,
      req.ip,
      null,
      { notes }
    );

    res.status(201).json({
      success: true,
      message: 'Activation key created successfully',
      data: {
        activationKey: {
          id: activationKey._id,
          key: activationKey.key,
          assignedTo: activationKey.assignedTo,
          validUntil: activationKey.validUntil,
          status: activationKey.status,
          createdAt: activationKey.createdAt
        }
      }
    });

  } catch (error) {
    console.error('Create activation key error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create activation key',
      code: 'CREATE_ACTIVATION_KEY_ERROR'
    });
  }
});

/**
 * POST /api/v1/admin/activation-keys/:keyId/revoke
 * Revoke an activation key
 */
router.post('/activation-keys/:keyId/revoke', validateObjectId('keyId'), async (req, res) => {
  try {
    const { keyId } = req.params;
    const { reason } = req.body;

    const activationKey = await ActivationKey.findById(keyId);
    if (!activationKey) {
      return res.status(404).json({
        success: false,
        error: 'Activation key not found',
        code: 'ACTIVATION_KEY_NOT_FOUND'
      });
    }

    if (activationKey.status === 'revoked') {
      return res.status(400).json({
        success: false,
        error: 'Activation key is already revoked',
        code: 'ALREADY_REVOKED'
      });
    }

    await activationKey.revoke(req.user._id, reason || 'Admin revocation');

    res.json({
      success: true,
      message: 'Activation key revoked successfully',
      data: {
        keyId: activationKey._id,
        revokedAt: activationKey.revokedAt,
        reason: activationKey.revocationReason
      }
    });

  } catch (error) {
    console.error('Revoke activation key error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to revoke activation key',
      code: 'REVOKE_ACTIVATION_KEY_ERROR'
    });
  }
});

/**
 * GET /api/v1/admin/analytics/errors
 * Get error analytics
 */
router.get('/analytics/errors', validateDateRange, async (req, res) => {
  try {
    const {
      startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
      endDate = new Date()
    } = req.query;

    const errorStats = await Activity.getErrorStats(
      new Date(startDate),
      new Date(endDate)
    );

    // Get error trends by day
    const errorTrends = await Activity.aggregate([
      {
        $match: {
          activityType: 'error',
          timestamp: {
            $gte: new Date(startDate),
            $lte: new Date(endDate)
          }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$timestamp' },
            month: { $month: '$timestamp' },
            day: { $dayOfMonth: '$timestamp' }
          },
          count: { $sum: 1 },
          criticalCount: {
            $sum: {
              $cond: [{ $eq: ['$error.severity', 'critical'] }, 1, 0]
            }
          }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
    ]);

    res.json({
      success: true,
      data: {
        period: { startDate: new Date(startDate), endDate: new Date(endDate) },
        errorStats,
        errorTrends
      }
    });

  } catch (error) {
    console.error('Get error analytics error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve error analytics',
      code: 'GET_ERROR_ANALYTICS_ERROR'
    });
  }
});

/**
 * GET /api/v1/admin/analytics/usage
 * Get usage analytics
 */
router.get('/analytics/usage', validateDateRange, async (req, res) => {
  try {
    const {
      startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
      endDate = new Date()
    } = req.query;

    // Get daily active users
    const dailyActiveUsers = await Activity.aggregate([
      {
        $match: {
          timestamp: {
            $gte: new Date(startDate),
            $lte: new Date(endDate)
          }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$timestamp' },
            month: { $month: '$timestamp' },
            day: { $dayOfMonth: '$timestamp' }
          },
          users: { $addToSet: '$userId' }
        }
      },
      {
        $project: {
          date: {
            $dateFromParts: {
              year: '$_id.year',
              month: '$_id.month',
              day: '$_id.day'
            }
          },
          userCount: { $size: '$users' }
        }
      },
      { $sort: { date: 1 } }
    ]);

    // Get feature usage
    const featureUsage = await Activity.aggregate([
      {
        $match: {
          timestamp: {
            $gte: new Date(startDate),
            $lte: new Date(endDate)
          }
        }
      },
      {
        $group: {
          _id: '$activityType',
          count: { $sum: 1 },
          uniqueUsers: { $addToSet: '$userId' }
        }
      },
      {
        $project: {
          activityType: '$_id',
          count: 1,
          uniqueUsers: { $size: '$uniqueUsers' }
        }
      },
      { $sort: { count: -1 } }
    ]);

    res.json({
      success: true,
      data: {
        period: { startDate: new Date(startDate), endDate: new Date(endDate) },
        dailyActiveUsers,
        featureUsage
      }
    });

  } catch (error) {
    console.error('Get usage analytics error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve usage analytics',
      code: 'GET_USAGE_ANALYTICS_ERROR'
    });
  }
});

/**
 * GET /api/v1/admin/activity-logs
 * Get activity logs with pagination and filtering
 */
router.get('/activity-logs', validatePagination, validateDateRange, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      userId,
      activityType,
      severity,
      search,
      startDate,
      endDate,
      sortBy = 'timestamp',
      sortOrder = 'desc'
    } = req.query;

    // Build query
    const query = {};

    if (userId) query.userId = userId;
    if (activityType) query.activityType = activityType;
    if (severity) query['error.severity'] = severity;

    if (startDate || endDate) {
      query.timestamp = {};
      if (startDate) query.timestamp.$gte = new Date(startDate);
      if (endDate) query.timestamp.$lte = new Date(endDate);
    }

    if (search) {
      query.$or = [
        { 'action.name': { $regex: search, $options: 'i' } },
        { 'screen.name': { $regex: search, $options: 'i' } },
        { 'error.message': { $regex: search, $options: 'i' } },
        { 'clinicalContext.category': { $regex: search, $options: 'i' } }
      ];
    }

    // Build sort object
    const sort = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    // Execute query with pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const activities = await Activity.find(query)
      .populate('userId', 'firstName lastName email role facility')
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit))
      .lean();

    // Get total count for pagination
    const total = await Activity.countDocuments(query);

    // Transform activities for admin UI
    const transformedActivities = activities.map(activity => ({
      id: activity._id,
      timestamp: activity.timestamp,
      userId: activity.userId?._id || activity.userId,
      userName: activity.userId ? `${activity.userId.firstName} ${activity.userId.lastName}` : 'Unknown User',
      userEmail: activity.userId?.email || 'N/A',
      userRole: activity.userId?.role || 'N/A',
      userFacility: activity.userId?.facility || 'N/A',
      activityType: activity.activityType,
      action: activity.action?.name || activity.activityType,
      details: getActivityDetails(activity),
      location: activity.location ?
        `${activity.location.address || 'Unknown Location'}${activity.location.facility ? ` (${activity.location.facility})` : ''}` :
        'Unknown',
      ipAddress: activity.metadata?.ip || 'N/A',
      deviceInfo: activity.deviceId || 'N/A',
      severity: activity.error?.severity || (activity.activityType === 'error' ? 'medium' : 'low'),
      clinicalContext: activity.clinicalContext,
      performance: activity.performance,
      error: activity.error,
      sessionId: activity.sessionId
    }));

    res.json({
      success: true,
      data: {
        activities: transformedActivities,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      }
    });

  } catch (error) {
    console.error('Get activity logs error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve activity logs',
      code: 'GET_ACTIVITY_LOGS_ERROR'
    });
  }
});

/**
 * GET /api/v1/admin/activity-logs/decision-support
 * Get detailed decision support activity logs
 */
router.get('/activity-logs/decision-support', validatePagination, validateDateRange, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      userId,
      startDate,
      endDate,
      sortBy = 'timestamp',
      sortOrder = 'desc'
    } = req.query;

    // Build query for decision support activities
    const query = {
      $or: [
        { activityType: { $regex: 'diagnosis', $options: 'i' } },
        { activityType: { $regex: 'clinical', $options: 'i' } },
        { 'screen.name': { $regex: 'clinical.*decision.*support', $options: 'i' } },
        { 'screen.name': { $regex: 'diagnosis', $options: 'i' } },
        { 'action.name': { $regex: 'diagnosis', $options: 'i' } },
        { 'action.name': { $regex: 'clinical', $options: 'i' } },
        { 'clinicalContext': { $exists: true } }
      ]
    };

    if (userId) query.userId = userId;

    if (startDate || endDate) {
      query.timestamp = {};
      if (startDate) query.timestamp.$gte = new Date(startDate);
      if (endDate) query.timestamp.$lte = new Date(endDate);
    }

    // Build sort object
    const sort = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    // Execute query with pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const activities = await Activity.find(query)
      .populate('userId', 'firstName lastName email role facility state')
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit))
      .lean();

    // Get total count for pagination
    const total = await Activity.countDocuments(query);

    // Transform activities with detailed decision support context
    const transformedActivities = activities.map(activity => ({
      id: activity._id,
      timestamp: activity.timestamp,
      user: {
        id: activity.userId?._id || activity.userId,
        name: activity.userId ? `${activity.userId.firstName} ${activity.userId.lastName}` : 'Unknown User',
        email: activity.userId?.email || 'N/A',
        role: activity.userId?.role || 'N/A',
        facility: activity.userId?.facility || 'N/A',
        state: activity.userId?.state || 'N/A'
      },
      activityType: activity.activityType,
      action: activity.action?.name || activity.activityType,
      screen: activity.screen?.name || 'Unknown Screen',
      details: getDecisionSupportDetails(activity),
      clinicalContext: activity.clinicalContext || {},
      patientInfo: extractPatientInfo(activity),
      recommendations: extractRecommendations(activity),
      performance: activity.performance || {},
      location: {
        address: activity.location?.address || 'Unknown',
        facility: activity.location?.facility || 'N/A',
        facilityType: activity.location?.facilityType || 'N/A',
        coordinates: activity.location?.latitude && activity.location?.longitude ?
          `${activity.location.latitude}, ${activity.location.longitude}` : 'N/A'
      },
      deviceInfo: {
        deviceId: activity.deviceId || 'N/A',
        sessionId: activity.sessionId || 'N/A'
      },
      duration: activity.performance?.duration || 0,
      successful: activity.interaction?.successful !== false,
      error: activity.error
    }));

    // Get summary statistics
    const summaryStats = await Activity.aggregate([
      { $match: query },
      {
        $group: {
          _id: null,
          totalActivities: { $sum: 1 },
          uniqueUsers: { $addToSet: '$userId' },
          avgDuration: { $avg: '$performance.duration' },
          successfulActivities: {
            $sum: {
              $cond: [{ $ne: ['$interaction.successful', false] }, 1, 0]
            }
          },
          clinicalCategories: { $addToSet: '$clinicalContext.category' },
          diagnosisTypes: { $addToSet: '$clinicalContext.diagnosisType' }
        }
      }
    ]);

    const stats = summaryStats[0] || {
      totalActivities: 0,
      uniqueUsers: [],
      avgDuration: 0,
      successfulActivities: 0,
      clinicalCategories: [],
      diagnosisTypes: []
    };

    res.json({
      success: true,
      data: {
        activities: transformedActivities,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        },
        summary: {
          totalActivities: stats.totalActivities,
          uniqueUsers: stats.uniqueUsers.length,
          avgDuration: Math.round(stats.avgDuration || 0),
          successRate: stats.totalActivities > 0 ?
            ((stats.successfulActivities / stats.totalActivities) * 100).toFixed(1) : '0',
          clinicalCategories: stats.clinicalCategories.filter(Boolean),
          diagnosisTypes: stats.diagnosisTypes.filter(Boolean)
        }
      }
    });

  } catch (error) {
    console.error('Get decision support activity logs error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve decision support activity logs',
      code: 'GET_DECISION_SUPPORT_LOGS_ERROR'
    });
  }
});

/**
 * GET /api/v1/admin/system/health
 * Get system health status
 */
router.get('/system/health', async (req, res) => {
  try {
    // Get database connection status
    const mongoose = require('mongoose');
    const dbStatus = mongoose.connection.readyState === 1 ? 'connected' : 'disconnected';

    // Get recent error rate
    const recentErrors = await Activity.countDocuments({
      activityType: 'error',
      timestamp: { $gte: new Date(Date.now() - 60 * 60 * 1000) } // Last hour
    });

    const recentActivities = await Activity.countDocuments({
      timestamp: { $gte: new Date(Date.now() - 60 * 60 * 1000) } // Last hour
    });

    const errorRate = recentActivities > 0 ? (recentErrors / recentActivities * 100).toFixed(2) : 0;

    // Get sync health
    const recentSyncs = await SyncLog.countDocuments({
      startedAt: { $gte: new Date(Date.now() - 60 * 60 * 1000) } // Last hour
    });

    const failedSyncs = await SyncLog.countDocuments({
      status: 'failed',
      startedAt: { $gte: new Date(Date.now() - 60 * 60 * 1000) } // Last hour
    });

    const syncSuccessRate = recentSyncs > 0 ? ((recentSyncs - failedSyncs) / recentSyncs * 100).toFixed(2) : 100;

    // Get active users in last 24 hours
    const activeUsers = await Activity.distinct('userId', {
      timestamp: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
    });

    res.json({
      success: true,
      data: {
        timestamp: new Date(),
        database: {
          status: dbStatus,
          healthy: dbStatus === 'connected'
        },
        errors: {
          recentCount: recentErrors,
          rate: parseFloat(errorRate),
          healthy: parseFloat(errorRate) < 5 // Less than 5% error rate is healthy
        },
        sync: {
          recentCount: recentSyncs,
          successRate: parseFloat(syncSuccessRate),
          healthy: parseFloat(syncSuccessRate) > 95 // More than 95% success rate is healthy
        },
        users: {
          active24h: activeUsers.length
        },
        overall: {
          healthy: dbStatus === 'connected' && parseFloat(errorRate) < 5 && parseFloat(syncSuccessRate) > 95
        }
      }
    });

  } catch (error) {
    console.error('Get system health error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve system health',
      code: 'GET_SYSTEM_HEALTH_ERROR'
    });
  }
});

// Helper functions
function getActivityDetails(activity) {
  if (activity.error) {
    return `Error: ${activity.error.message}`;
  }

  if (activity.clinicalContext) {
    const context = activity.clinicalContext;
    return `Clinical activity: ${context.category || 'Unknown'} - ${context.diagnosisId || context.recordId || 'N/A'}`;
  }

  if (activity.action) {
    return `${activity.action.name}: ${activity.action.value || activity.action.target || 'N/A'}`;
  }

  return `${activity.activityType} activity`;
}

function getDecisionSupportDetails(activity) {
  const details = [];

  if (activity.clinicalContext) {
    const ctx = activity.clinicalContext;
    if (ctx.category) details.push(`Category: ${ctx.category}`);
    if (ctx.severity) details.push(`Severity: ${ctx.severity}`);
    if (ctx.diagnosisId) details.push(`Diagnosis ID: ${ctx.diagnosisId}`);
  }

  if (activity.action) {
    details.push(`Action: ${activity.action.name}`);
    if (activity.action.value) details.push(`Value: ${activity.action.value}`);
  }

  if (activity.performance?.duration) {
    details.push(`Duration: ${Math.round(activity.performance.duration / 1000)}s`);
  }

  return details.join(' | ') || getActivityDetails(activity);
}

function extractPatientInfo(activity) {
  if (activity.action?.metadata?.patientInfo) {
    return activity.action.metadata.patientInfo;
  }

  if (activity.action?.value && typeof activity.action.value === 'object') {
    const value = activity.action.value;
    if (value.age || value.symptoms || value.chiefComplaint) {
      return {
        age: value.age,
        ageGroup: value.ageGroup,
        symptoms: value.symptoms,
        chiefComplaint: value.chiefComplaint,
        vitalSigns: value.vitalSigns
      };
    }
  }

  return null;
}

function extractRecommendations(activity) {
  if (activity.action?.metadata?.recommendations) {
    return activity.action.metadata.recommendations;
  }

  if (activity.action?.value?.recommendations) {
    return activity.action.value.recommendations;
  }

  return null;
}

module.exports = router;
